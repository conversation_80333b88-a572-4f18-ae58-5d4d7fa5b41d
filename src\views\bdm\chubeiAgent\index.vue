<template>
  <div id="agentRepres">
    <div class="targetMange_Box">
      <div class="filterForm_box">
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.name"
          placeholder="代理商名称"
          allowClear
          @change="getList()"
          @press-enter="getList()"
        />
        <a-select
          v-model:value="searchForm.type"
          style="width: 240px"
          placeholder="代理商类型"
          :options="typeOptions"
          :field-names="{ label: 'name', value: 'value' }"
          allowClear
          @change="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.userName"
          placeholder="招商经理"
          allowClear
          @change="getList()"
          @press-enter="getList()"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-button type="primary" @click="handleAdd()">新增</a-button>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <!-- <template #sex="{ record }">
            <span>{{ record.sex == 1 ? '男' : record.sex == 2 ? '女' : '' }}</span>
          </template> -->
          <!-- { record } -->
          <template #action="{ record }">
            <a-button type="link" @click.stop="onEdit(record)">编辑</a-button>
            <a-button type="link" @click.stop="onDel(record)">删除</a-button>
            <a-button type="link" @click.stop="onDel(record)">查看</a-button>
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <a-modal
    v-model:visible="openSignVisible"
    title="新增"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleModalOk"
    @cancel="() => (openSignVisible = false)"
    width="100%"
    wrap-class-name="full-modal-chubeiAgent"
  >
    <div class="modal_box">
      <a-form
        class="form_box"
        ref="formRef"
        :model="modalInfo"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        autocomplete="off"
        labelAlign="right"
        :colon="true"
      >
        <a-row>
          <a-col class="import-title">基础信息</a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="代理商名称" name="name">
              <a-input v-model:value="modalInfo.name" placeholder="请输入代理商名称" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="代理商编号" name="code">
              <a-input v-model:value="modalInfo.code" disabled placeholder="" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="代理商类型" name="type">
              <a-select
                v-model:value="modalInfo.type"
                style="width: 240px"
                placeholder="请选择代理商类型"
                :options="typeOptions"
                :field-names="{ label: 'name', value: 'value' }"
                allowClear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="联系电话" name="name">
              <a-input v-model:value="modalInfo.name" placeholder="请输入联系电话" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              :label="modalInfo.type === '1' ? '企业统一社会信用代码' : '身份证号码'"
              name="idNumber"
            >
              <a-input v-model:value="modalInfo.idNumber" placeholder="" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="团队人员" name="type">
              <a-select
                v-model:value="modalInfo.type"
                style="width: 240px"
                placeholder="请选择团队人员"
                :options="typeOptions"
                :field-names="{ label: 'name', value: 'value' }"
                allowClear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="所在省份" name="name">
              <a-select
                v-model:value="modalInfo.type"
                style="width: 240px"
                placeholder="请选择所在省份"
                :options="typeOptions"
                :field-names="{ label: 'name', value: 'value' }"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="所在城市" name="code">
              <a-select
                v-model:value="modalInfo.type"
                style="width: 240px"
                placeholder="请选择所在城市"
                :options="typeOptions"
                :field-names="{ label: 'name', value: 'value' }"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="详细地址" name="type">
              <a-input v-model:value="modalInfo.name" placeholder="请输入详细地址" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="主要覆盖区域" name="name">
              <a-select
                v-model:value="modalInfo.type"
                style="width: 240px"
                placeholder="请选择省份"
                :options="typeOptions"
                :field-names="{ label: 'name', value: 'value' }"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="主要覆盖区域" name="code">
              <a-select
                v-model:value="modalInfo.type"
                style="width: 240px"
                placeholder="请选择城市"
                :options="typeOptions"
                :field-names="{ label: 'name', value: 'value' }"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="详细地址" name="type">
              <a-input v-model:value="modalInfo.name" placeholder="请输入详细地址" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import cTable from '/@/views/dailyWork/components/Table/index.vue';
  import { onMounted, reactive, ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  import {
    getAgentList,
    getAgentDetail,
    getProductSelect,
    getProvince,
    getCityByProvinceCode,
    addAgent,
    editAgent,
    deleteAgent,
  } from '/@/api/bdm/chubeiAgent';
  import { getDicDetailList } from '/@/api/system/dic';
  import dayjs from 'dayjs';
  import { debounce } from 'lodash-es';

  const typeOptions: any = ref([]);
  const searchForm = reactive({
    name: '',
    userName: '',
    type: null,
  });
  const reSet = () => {
    searchForm.name = '';
    searchForm.userName = '';
    searchForm.type = null;
    getList();
  };
  // 打开导入弹窗
  const handleAdd = () => {
    openSignVisible.value = true;
  };
  const tableColumns = [
    {
      title: '序号',
      isHasIndex: true,
      key: 'No',
      align: 'center',
      width: 50,
    },
    {
      title: '代理商名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '代理商编码',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: '所在省份',
      dataIndex: 'provinceName',
      key: 'provinceName',
      align: 'center',
    },
    {
      title: '主要覆盖省份',
      dataIndex: 'primaryProvinceName',
      key: 'primaryProvinceName',
      align: 'center',
    },
    {
      title: '团队人员数量',
      dataIndex: 'peopleNumber',
      key: 'peopleNumber',
      align: 'center',
    },
    {
      title: '财税能力',
      dataIndex: 'fiscalCompetence',
      key: 'fiscalCompetence',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ];
  const tableData = reactive<{ data: any[] }>({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 0,
    pageSize: 10,
  });
  const getList = debounce(async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getAgentList(temp);
      pagination.totalItems = res.total ?? 0;
      tableData.data = res.list;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  }, 200);
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };
  const onDel = async (row: any) => {
    try {
      let res = await deleteAgent([row.id]);
      if (res) {
        notification.success({
          message: '提示',
          description: '删除成功',
        });
        getList(1);
      }
    } catch (err) {
      console.log(err);
    }
  };

  onMounted(async () => {
    getList();
    const res = await getDicDetailList({ itemId: '1866686246655471618' });
    typeOptions.value = res;
  });
  const openSignVisible = ref(false);
  type ModelInfo = {
    name: string;
    userName: string;
    type: string;
  };
  const modalInfo = ref<ModelInfo>({
    name: '',
    userName: '',
    type: '',
  });
  const modalLoading = ref<boolean>(false);
  const formRef = ref();
  const onEdit = (row: any) => {
    getAgentDetail({ id: row.id }).then((res: any) => {
      modalInfo.value = {
        ...res,
      };
      openSignVisible.value = true;
    });
  };
  const handleModalOk = () => {
    formRef.value
      .validate()
      .then(async () => {
        try {
          openSignVisible.value = true;
          let temp = {
            ...modalInfo.value,
          };
          await editAgent(temp);
          notification.success({
            message: '提示',
            description: '操作成功',
          });
          openSignVisible.value = false;
          getList(1);
        } catch (error) {
          openSignVisible.value = false;
        }
        openSignVisible.value = false;
      })
      .catch((error) => {
        console.log('error', error);
      });
  };
</script>

<style scoped lang="less">
  #agentRepres {
    width: 100%;
    height: 100%;
    padding: 8px;
    .targetMange_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        padding: 16px;
        &:last-child {
          flex: 1;
          height: 0;
        }
      }
      .tabs {
        padding: 0 16px;
      }
      .p_box {
        padding: 0 16px;
      }
      .type_top {
        width: 100%;
        padding: 16px 0 0 16px;
        .btn_group {
          display: inline-flex;
          > div {
            padding: 6px 40px;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            cursor: pointer;
            &:first-child {
              border-right: none;
              border-radius: 3px 0 0 3px;
            }
            &:last-child {
              border-left: none;
              border-radius: 0 3px 3px 0;
            }
          }
          .is_active {
            color: #fff;
          }
        }
      }
    }
    .filterForm_box {
      * + * {
        margin-left: 16px;
      }
      .right_btn {
        float: right;
      }
    }
    .table_box {
      padding-bottom: 0 !important;
      .tipRed {
        color: #c10000;
      }
    }
  }
  .modal_box {
    padding: 16px;
  }
</style>
<style lang="less">
  .full-modal-chubeiAgent {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }
    .ant-modal-body {
      flex: 1;
    }
  }
</style>
